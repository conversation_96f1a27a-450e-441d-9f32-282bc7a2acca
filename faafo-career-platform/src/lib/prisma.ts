import { PrismaClient } from "@prisma/client";

// Global Prisma instance to prevent multiple connections in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Build optimized database URL with connection pool parameters
function buildDatabaseUrl(): string {
  const baseUrl = process.env.POSTGRES_PRISMA_URL || process.env.DATABASE_URL;

  if (!baseUrl) {
    throw new Error('DATABASE_URL or POSTGRES_PRISMA_URL must be defined');
  }

  // If URL already has parameters, don't modify it
  if (baseUrl.includes('?')) {
    return baseUrl;
  }

  // Add connection pool parameters for PostgreSQL with improved settings
  const poolParams = new URLSearchParams({
    'connection_limit': '20', // Increased for better concurrency
    'pool_timeout': '30', // Increased timeout
    'connect_timeout': '30', // Increased connection timeout
    'socket_timeout': '30', // Increased socket timeout
    'statement_timeout': '60000', // Increased to 60 seconds for complex queries
    'idle_timeout': '600', // Increased idle timeout to 10 minutes
    'pgbouncer': 'true',
    'prepared_statements': 'false' // Disable for better compatibility with connection pooling
  });

  return `${baseUrl}?${poolParams.toString()}`;
}

const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: buildDatabaseUrl(),
    },
  },
  // Optimize transaction settings for better reliability
  transactionOptions: {
    maxWait: 10000, // Increased to 10 seconds
    timeout: 30000, // Increased to 30 seconds for complex operations
    isolationLevel: 'ReadCommitted', // Better for concurrent operations
  },
  // Add error handling and retry logic
  errorFormat: 'pretty',
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Improved graceful shutdown handler with better error handling
let isShuttingDown = false;

const gracefulShutdown = async (signal: string) => {
  if (isShuttingDown) {
    console.log(`Already shutting down, ignoring ${signal}`);
    return;
  }

  isShuttingDown = true;
  console.log(`Received ${signal}, starting graceful shutdown...`);

  try {
    // Give ongoing operations time to complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Disconnect Prisma client
    await prisma.$disconnect();
    console.log('Prisma client disconnected successfully');

    process.exit(0);
  } catch (error) {
    console.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

process.on('beforeExit', async () => {
  if (!isShuttingDown) {
    console.log('Process exiting, disconnecting Prisma client...');
    try {
      await prisma.$disconnect();
    } catch (error) {
      console.error('Error disconnecting Prisma on beforeExit:', error);
    }
  }
});

process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// Database operation wrapper with retry logic
export async function withDatabaseRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      // Check if it's a connection error that we should retry
      const isRetryableError =
        error instanceof Error && (
          error.message.includes('connection') ||
          error.message.includes('timeout') ||
          error.message.includes('ECONNRESET') ||
          error.message.includes('ENOTFOUND') ||
          error.message.includes('ETIMEDOUT')
        );

      if (!isRetryableError || attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      console.warn(`Database operation failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${Math.round(delay)}ms:`, error.message);

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

export { prisma };
export default prisma;