import { PrismaClient } from "@prisma/client";

// Global Prisma instance to prevent multiple connections in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

// Build optimized database URL with connection pool parameters
function buildDatabaseUrl(): string {
  const baseUrl = process.env.POSTGRES_PRISMA_URL || process.env.DATABASE_URL;

  if (!baseUrl) {
    throw new Error('DATABASE_URL or POSTGRES_PRISMA_URL must be defined');
  }

  // If URL already has parameters, don't modify it
  if (baseUrl.includes('?')) {
    return baseUrl;
  }

  // Add connection pool parameters for PostgreSQL with improved settings
  const poolParams = new URLSearchParams({
    'connection_limit': '20', // Increased for better concurrency
    'pool_timeout': '30', // Increased timeout
    'connect_timeout': '30', // Increased connection timeout
    'socket_timeout': '30', // Increased socket timeout
    'statement_timeout': '60000', // Increased to 60 seconds for complex queries
    'idle_timeout': '600', // Increased idle timeout to 10 minutes
    'pgbouncer': 'true',
    'prepared_statements': 'false' // Disable for better compatibility with connection pooling
  });

  return `${baseUrl}?${poolParams.toString()}`;
}

// Create Prisma client with aggressive connection management
const createPrismaClient = () => new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: buildDatabaseUrl(),
    },
  },
  // Optimize transaction settings for maximum reliability
  transactionOptions: {
    maxWait: 15000, // Increased to 15 seconds
    timeout: 60000, // Increased to 60 seconds for complex operations
    isolationLevel: 'ReadCommitted', // Better for concurrent operations
  },
  // Add error handling and retry logic
  errorFormat: 'pretty',
});

const prisma = globalForPrisma.prisma ?? createPrismaClient();

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Improved graceful shutdown handler with better error handling
let isShuttingDown = false;

const gracefulShutdown = async (signal: string) => {
  if (isShuttingDown) {
    console.log(`Already shutting down, ignoring ${signal}`);
    return;
  }

  isShuttingDown = true;
  console.log(`Received ${signal}, starting graceful shutdown...`);

  try {
    // Give ongoing operations time to complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Disconnect Prisma client
    await prisma.$disconnect();
    console.log('Prisma client disconnected successfully');

    process.exit(0);
  } catch (error) {
    console.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

process.on('beforeExit', async () => {
  if (!isShuttingDown) {
    console.log('Process exiting, disconnecting Prisma client...');
    try {
      await prisma.$disconnect();
    } catch (error) {
      console.error('Error disconnecting Prisma on beforeExit:', error);
    }
  }
});

process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// Connection health monitoring
let connectionHealthy = true;
let lastHealthCheck = 0;
const HEALTH_CHECK_INTERVAL = 30000; // 30 seconds

async function checkConnectionHealth(): Promise<boolean> {
  const now = Date.now();
  if (now - lastHealthCheck < HEALTH_CHECK_INTERVAL && connectionHealthy) {
    return connectionHealthy;
  }

  try {
    await prisma.$queryRaw`SELECT 1`;
    connectionHealthy = true;
    lastHealthCheck = now;
    return true;
  } catch (error) {
    console.warn('Database connection health check failed:', error);
    connectionHealthy = false;
    lastHealthCheck = now;
    return false;
  }
}

// Database operation wrapper with aggressive retry logic and health monitoring
export async function withDatabaseRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 5,
  baseDelay: number = 500
): Promise<T> {
  let lastError: Error;

  // Check connection health first
  const isHealthy = await checkConnectionHealth();
  if (!isHealthy) {
    console.warn('Database connection unhealthy, attempting to reconnect...');
    try {
      await prisma.$disconnect();
      await new Promise(resolve => setTimeout(resolve, 1000));
      await prisma.$connect();
      console.log('Database reconnection successful');
    } catch (reconnectError) {
      console.error('Database reconnection failed:', reconnectError);
    }
  }

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const result = await operation();

      // Mark connection as healthy on successful operation
      if (!connectionHealthy) {
        connectionHealthy = true;
        console.log('Database connection restored');
      }

      return result;
    } catch (error) {
      lastError = error as Error;

      // Check if it's a connection error that we should retry
      const isRetryableError =
        error instanceof Error && (
          error.message.includes('connection') ||
          error.message.includes('timeout') ||
          error.message.includes('ECONNRESET') ||
          error.message.includes('ENOTFOUND') ||
          error.message.includes('ETIMEDOUT') ||
          error.message.includes('Closed') ||
          error.message.includes('EPIPE') ||
          error.message.includes('ECONNREFUSED')
        );

      if (!isRetryableError || attempt === maxRetries) {
        connectionHealthy = false;
        throw error;
      }

      // Mark connection as unhealthy
      connectionHealthy = false;

      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 500;
      console.warn(`Database operation failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${Math.round(delay)}ms:`, error.message);

      // Try to reconnect on connection errors
      if (attempt > 0) {
        try {
          await prisma.$disconnect();
          await new Promise(resolve => setTimeout(resolve, 500));
          await prisma.$connect();
        } catch (reconnectError) {
          console.warn('Reconnection attempt failed:', reconnectError);
        }
      }

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

export { prisma };
export default prisma;