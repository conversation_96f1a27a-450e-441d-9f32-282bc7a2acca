/**
 * Self-Healing AI Service
 * 
 * Provides circuit breaker pattern, automatic fallbacks, and intelligent retry
 * mechanisms for AI service calls to prevent failures and ensure reliability.
 */

import { geminiService } from '@/lib/services/geminiService';

export interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime: number;
  successCount: number;
  halfOpenAttempts: number;
}

export interface AIServiceOptions {
  timeout?: number;
  maxRetries?: number;
  fallbackToStatic?: boolean;
  circuitBreakerThreshold?: number;
  circuitBreakerTimeout?: number;
}

export interface AIServiceResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  source: 'ai' | 'fallback' | 'cache' | 'contextual_fallback';
  responseTime: number;
  retryCount: number;
}

export class SelfHealingAIService {
  private static circuitBreakers = new Map<string, CircuitBreakerState>();
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  private static readonly DEFAULT_OPTIONS: Required<AIServiceOptions> = {
    timeout: 5000, // 5 seconds for faster fallback
    maxRetries: 1, // Only 1 retry for faster fallback
    fallbackToStatic: true,
    circuitBreakerThreshold: 2, // Open circuit faster
    circuitBreakerTimeout: 30000 // 30 seconds
  };

  /**
   * Generate interview questions with self-healing capabilities
   */
  static async generateInterviewQuestions(
    sessionConfig: any,
    options: AIServiceOptions = {}
  ): Promise<AIServiceResult<any>> {
    const startTime = Date.now();
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const cacheKey = this.generateCacheKey('questions', sessionConfig);
    
    // Check cache first
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        source: 'cache',
        responseTime: Date.now() - startTime,
        retryCount: 0
      };
    }

    // Check circuit breaker
    const circuitBreaker = this.getCircuitBreaker('question-generation');
    if (circuitBreaker.isOpen) {
      if (Date.now() - circuitBreaker.lastFailureTime < opts.circuitBreakerTimeout) {
        // Circuit is open, use fallback immediately
        return this.fallbackQuestionGeneration(sessionConfig, startTime);
      } else {
        // Try to close circuit (half-open state)
        circuitBreaker.isOpen = false;
        circuitBreaker.halfOpenAttempts = 0;
      }
    }

    // Attempt AI generation with retries
    for (let attempt = 0; attempt <= opts.maxRetries; attempt++) {
      try {
        const result = await this.callAIWithTimeout(
          () => geminiService.generateInterviewQuestions(sessionConfig),
          opts.timeout
        );

        // Success - update circuit breaker
        this.recordSuccess('question-generation');
        
        // Cache the result
        this.setCache(cacheKey, result, 300000); // 5 minutes TTL
        
        return {
          success: true,
          data: result,
          source: 'ai',
          responseTime: Date.now() - startTime,
          retryCount: attempt
        };

      } catch (error) {
        console.error(`AI question generation attempt ${attempt + 1} failed:`, error);
        
        // Record failure
        this.recordFailure('question-generation');
        
        // If this is the last attempt and fallback is enabled, use fallback
        if (attempt === opts.maxRetries && opts.fallbackToStatic) {
          return this.fallbackQuestionGeneration(sessionConfig, startTime, attempt + 1);
        }
        
        // Wait before retry (exponential backoff)
        if (attempt < opts.maxRetries) {
          await this.delay(Math.pow(2, attempt) * 1000);
        }
      }
    }

    // All retries failed
    return {
      success: false,
      error: 'AI service unavailable after all retries',
      source: 'ai',
      responseTime: Date.now() - startTime,
      retryCount: opts.maxRetries + 1
    };
  }

  /**
   * Analyze interview response with self-healing capabilities
   */
  static async analyzeInterviewResponse(
    questionText: string,
    responseText: string,
    options: AIServiceOptions = {}
  ): Promise<AIServiceResult<any>> {
    const startTime = Date.now();
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const cacheKey = this.generateCacheKey('analysis', { questionText, responseText });
    
    // Check cache first
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return {
        success: true,
        data: cached,
        source: 'cache',
        responseTime: Date.now() - startTime,
        retryCount: 0
      };
    }

    // Check circuit breaker
    const circuitBreaker = this.getCircuitBreaker('response-analysis');
    if (circuitBreaker.isOpen) {
      if (Date.now() - circuitBreaker.lastFailureTime < opts.circuitBreakerTimeout) {
        // Circuit is open, use fallback immediately
        return this.fallbackResponseAnalysis(questionText, responseText, startTime);
      } else {
        // Try to close circuit (half-open state)
        circuitBreaker.isOpen = false;
        circuitBreaker.halfOpenAttempts = 0;
      }
    }

    // Attempt AI analysis with retries
    for (let attempt = 0; attempt <= opts.maxRetries; attempt++) {
      try {
        const result = await this.callAIWithTimeout(
          () => geminiService.analyzeInterviewResponse({
            questionText,
            questionType: 'BEHAVIORAL', // Default type for fallback
            questionCategory: 'GENERAL',
            responseText,
            responseTime: 0,
            expectedDuration: 180,
          }),
          opts.timeout
        );

        // Success - update circuit breaker
        this.recordSuccess('response-analysis');
        
        // Cache the result
        this.setCache(cacheKey, result, 600000); // 10 minutes TTL
        
        return {
          success: true,
          data: result,
          source: 'ai',
          responseTime: Date.now() - startTime,
          retryCount: attempt
        };

      } catch (error) {
        console.error(`AI response analysis attempt ${attempt + 1} failed:`, error);
        
        // Record failure
        this.recordFailure('response-analysis');
        
        // If this is the last attempt and fallback is enabled, use fallback
        if (attempt === opts.maxRetries && opts.fallbackToStatic) {
          return this.fallbackResponseAnalysis(questionText, responseText, startTime, attempt + 1);
        }
        
        // Wait before retry (exponential backoff)
        if (attempt < opts.maxRetries) {
          await this.delay(Math.pow(2, attempt) * 1000);
        }
      }
    }

    // All retries failed
    return {
      success: false,
      error: 'AI service unavailable after all retries',
      source: 'ai',
      responseTime: Date.now() - startTime,
      retryCount: opts.maxRetries + 1
    };
  }

  /**
   * Fallback question generation using static question banks
   */
  private static fallbackQuestionGeneration(
    sessionConfig: any,
    startTime: number,
    retryCount: number = 0
  ): AIServiceResult<any> {
    // Skip the complex question bank import and go directly to basic fallback
    // This ensures questions always load even if the question banks are not available
    console.log('Using basic fallback questions for reliable loading');
    return this.basicFallbackQuestions(sessionConfig, startTime, retryCount);
  }

  /**
   * Basic fallback questions when question banks are not available
   */
  private static basicFallbackQuestions(
    sessionConfig: any,
    startTime: number,
    retryCount: number = 0
  ): AIServiceResult<any> {
    const basicQuestions = [
      {
        questionText: "Tell me about yourself and your professional background.",
        questionType: "BEHAVIORAL",
        category: "GENERAL",
        difficulty: sessionConfig.difficulty || "INTERMEDIATE",
        expectedDuration: 180,
        context: "This is a common opening question to assess communication skills.",
        hints: {
          structure: "Use a brief professional summary highlighting relevant experience",
          keyPoints: ["Current role", "Key achievements", "Career goals"]
        },
        isRequired: true,
        priority: 1
      },
      {
        questionText: "Why are you interested in this position?",
        questionType: "BEHAVIORAL",
        category: "GENERAL",
        difficulty: sessionConfig.difficulty || "INTERMEDIATE",
        expectedDuration: 120,
        context: "Assesses motivation and research about the role.",
        hints: {
          structure: "Connect your skills and interests to the role requirements",
          keyPoints: ["Role alignment", "Company research", "Career goals"]
        },
        isRequired: true,
        priority: 1
      },
      {
        questionText: "Describe a challenging situation you faced at work and how you handled it.",
        questionType: "BEHAVIORAL",
        category: "PROBLEM_SOLVING",
        difficulty: sessionConfig.difficulty || "INTERMEDIATE",
        expectedDuration: 240,
        context: "Uses STAR method to assess problem-solving skills.",
        hints: {
          structure: "Use STAR method: Situation, Task, Action, Result",
          keyPoints: ["Clear problem description", "Your specific actions", "Positive outcome"]
        },
        isRequired: true,
        priority: 1
      },
      {
        questionText: "What are your greatest strengths and how do they apply to this role?",
        questionType: "BEHAVIORAL",
        category: "SOFT_SKILLS",
        difficulty: sessionConfig.difficulty || "INTERMEDIATE",
        expectedDuration: 150,
        context: "Evaluates self-awareness and role alignment.",
        hints: {
          structure: "Choose 2-3 relevant strengths with specific examples",
          keyPoints: ["Role-relevant strengths", "Concrete examples", "Impact on work"]
        },
        isRequired: true,
        priority: 1
      },
      {
        questionText: "Where do you see yourself in 5 years?",
        questionType: "BEHAVIORAL",
        category: "GENERAL",
        difficulty: sessionConfig.difficulty || "INTERMEDIATE",
        expectedDuration: 120,
        context: "Assesses career planning and long-term commitment.",
        hints: {
          structure: "Show growth mindset while staying realistic",
          keyPoints: ["Career progression", "Skill development", "Value to company"]
        },
        isRequired: true,
        priority: 1
      }
    ];

    const totalQuestions = Math.min(sessionConfig.totalQuestions || 5, basicQuestions.length);
    const selectedQuestions = basicQuestions.slice(0, totalQuestions);

    return {
      success: true,
      data: { questions: selectedQuestions },
      source: 'contextual_fallback',
      responseTime: Date.now() - startTime,
      retryCount
    };
  }

  /**
   * Fallback response analysis using rule-based scoring
   */
  private static fallbackResponseAnalysis(
    questionText: string,
    responseText: string,
    startTime: number,
    retryCount: number = 0
  ): AIServiceResult<any> {
    // Simple rule-based analysis
    const wordCount = responseText.split(/\s+/).length;
    const hasStructure = /first|second|third|finally|in conclusion/i.test(responseText);
    const hasExamples = /for example|such as|instance|specifically/i.test(responseText);
    
    let score = 5; // Base score
    
    // Adjust score based on response characteristics
    if (wordCount > 50) score += 1;
    if (wordCount > 100) score += 1;
    if (hasStructure) score += 1;
    if (hasExamples) score += 1;
    if (wordCount < 20) score -= 2;
    
    score = Math.max(1, Math.min(10, score));
    
    return {
      success: true,
      data: {
        aiScore: score,
        aiAnalysis: {
          wordCount,
          hasStructure,
          hasExamples,
          fallbackAnalysis: true
        },
        feedback: {
          strengths: wordCount > 50 ? ['Good response length'] : [],
          improvements: wordCount < 50 ? ['Consider providing more detail'] : []
        }
      },
      source: 'fallback',
      responseTime: Date.now() - startTime,
      retryCount
    };
  }

  // Helper methods
  private static async callAIWithTimeout<T>(fn: () => Promise<T>, timeout: number): Promise<T> {
    return Promise.race([
      fn(),
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('AI service timeout')), timeout)
      )
    ]);
  }

  private static getCircuitBreaker(service: string): CircuitBreakerState {
    if (!this.circuitBreakers.has(service)) {
      this.circuitBreakers.set(service, {
        isOpen: false,
        failureCount: 0,
        lastFailureTime: 0,
        successCount: 0,
        halfOpenAttempts: 0
      });
    }
    return this.circuitBreakers.get(service)!;
  }

  private static recordSuccess(service: string): void {
    const breaker = this.getCircuitBreaker(service);
    breaker.successCount++;
    breaker.failureCount = 0;
    breaker.isOpen = false;
  }

  private static recordFailure(service: string): void {
    const breaker = this.getCircuitBreaker(service);
    breaker.failureCount++;
    breaker.lastFailureTime = Date.now();
    
    if (breaker.failureCount >= this.DEFAULT_OPTIONS.circuitBreakerThreshold) {
      breaker.isOpen = true;
      console.warn(`Circuit breaker opened for service: ${service}`);
    }
  }

  private static generateCacheKey(operation: string, data: any): string {
    return `${operation}:${JSON.stringify(data)}`;
  }

  private static getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key);
    }
    return null;
  }

  private static setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default SelfHealingAIService;
