import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withErrorHandler } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import SessionSecurity from '@/lib/session-security';
import { withSecureErrorHandling } from '@/lib/secure-error-handler';
import { UserValidationService } from '@/lib/user-validation-service';
import { z } from 'zod';

// Validation schema for updating session
const updateSessionSchema = z.object({
  status: z.enum(['IN_PROGRESS', 'PAUSED', 'COMPLETED', 'ABANDONED']).optional(),
  timeSpent: z.number().min(0).optional(),
  overallScore: z.number().min(0).max(10).optional(),
  aiInsights: z.object({
    strengths: z.array(z.string()).optional(),
    improvements: z.array(z.string()).optional(),
    overallFeedback: z.string().max(2000).optional(),
    score: z.number().min(0).max(10).optional(),
    recommendations: z.array(z.string()).optional(),
  }).optional(),
});

// GET - Retrieve specific interview session
export const GET = withSecureErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 200 : 50 // Higher limit for development
    },
    async () => {
      const { sessionId } = await params;

      // Enhanced user validation first
      const userValidation = await UserValidationService.validateUserSession(request, {
        validateUserExists: true,
        checkAccountLock: true
      });

      if (!userValidation.isValid) {
        return NextResponse.json(
          { success: false, error: userValidation.error },
          { status: userValidation.statusCode || 401 }
        );
      }

      // Then validate session access
      const sessionValidation = await UserValidationService.validateUserResourceAccess(
        userValidation.userId!,
        'interview_session',
        sessionId
      );

      if (!sessionValidation.isValid) {
        return NextResponse.json(
          { success: false, error: sessionValidation.error },
          { status: sessionValidation.statusCode || 404 }
        );
      }

      try {
        const interviewSession = await prisma.interviewSession.findFirst({
          where: {
            id: sessionId,
            userId: sessionValidation.userId,
          },
          include: {
            questions: {
              include: {
                responses: {
                  where: { userId: sessionValidation.userId },
                  select: {
                    id: true,
                    responseText: true,
                    audioUrl: true,
                    responseTime: true,
                    preparationTime: true,
                    aiScore: true,
                    aiAnalysis: true,
                    feedback: true,
                    isCompleted: true,
                    userNotes: true,
                    createdAt: true,
                    updatedAt: true,
                  },
                },
              },
              orderBy: { questionOrder: 'asc' },
            },
            responses: {
              where: { userId: sessionValidation.userId },
              select: {
                id: true,
                questionId: true,
                isCompleted: true,
                aiScore: true,
                responseTime: true,
              },
            },
          },
        });

        if (!interviewSession) {
          return NextResponse.json(
            { success: false, error: 'Interview session not found' },
            { status: 404 }
          );
        }

        // Calculate progress
        const completedQuestions = interviewSession.responses.filter(r => r.isCompleted).length;
        const progressPercentage = interviewSession.totalQuestions > 0 
          ? Math.round((completedQuestions / interviewSession.totalQuestions) * 100)
          : 0;

        const sessionWithProgress = {
          ...interviewSession,
          progress: {
            completed: completedQuestions,
            total: interviewSession.totalQuestions,
            percentage: progressPercentage,
          },
        };

        return NextResponse.json({
          success: true,
          data: sessionWithProgress,
        });
      } catch (error) {
        // Error will be handled by withSecureErrorHandling wrapper
        throw error;
      }
    }
  );
});

// PATCH - Update interview session
export const PATCH = withSecureErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      {
        windowMs: 15 * 60 * 1000,
        maxRequests: process.env.NODE_ENV === 'development' ? 100 : 20 // Higher limit for development
      },
      async () => {
      const { sessionId } = await params;

      // Enhanced session validation
      const sessionValidation = await SessionSecurity.validateSessionAccess(
        request,
        sessionId,
        'interview'
      );

      if (!sessionValidation.isValid) {
        return NextResponse.json(
          { success: false, error: sessionValidation.error },
          { status: sessionValidation.statusCode || 400 }
        );
      }

      try {
        const body = await request.json();
        const validation = updateSessionSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const updateData = validation.data;

        // Validate status transition if status is being updated
        if (updateData.status) {
          const currentSession = await prisma.interviewSession.findUnique({
            where: { id: sessionId },
            select: { status: true }
          });

          if (currentSession) {
            const transitionValidation = SessionSecurity.validateSessionStateTransition(
              currentSession.status,
              updateData.status,
              'interview'
            );

            if (!transitionValidation.isValid) {
              return NextResponse.json(
                { success: false, error: transitionValidation.error },
                { status: 400 }
              );
            }
          }
        }

        // Update session
        const updatedSession = await prisma.interviewSession.update({
          where: { id: sessionId },
          data: {
            ...updateData,
            lastActiveAt: new Date(),
            ...(updateData.status === 'COMPLETED'
              ? { completedAt: new Date() }
              : {}),
          },
          include: {
            questions: {
              select: {
                id: true,
                questionType: true,
                category: true,
                difficulty: true,
                questionOrder: true,
              },
              orderBy: { questionOrder: 'asc' }
            },
            responses: {
              where: { userId: sessionValidation.userId },
              select: {
                id: true,
                questionId: true,
                isCompleted: true,
                aiScore: true,
              },
            },
          },
        });

        return NextResponse.json({
          success: true,
          data: updatedSession,
          message: 'Interview session updated successfully',
        });
      } catch (error) {
        // Error will be handled by withSecureErrorHandling wrapper
        throw error;
      }
    }
    );
  });
});

// DELETE - Delete interview session
export const DELETE = withSecureErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ sessionId: string }> }
) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 deletions per 15 minutes
      async () => {
      const { sessionId } = await params;

      // Enhanced session validation
      const validation = await SessionSecurity.validateSessionAccess(
        request,
        sessionId,
        'interview'
      );

      if (!validation.isValid) {
        return NextResponse.json(
          { success: false, error: validation.error },
          { status: validation.statusCode || 400 }
        );
      }

      try {

        // Delete session (cascade will handle questions and responses)
        await prisma.interviewSession.delete({
          where: { id: sessionId },
        });

        return NextResponse.json({
          success: true,
          message: 'Interview session deleted successfully',
        });
      } catch (error) {
        // Error will be handled by withSecureErrorHandling wrapper
        throw error;
      }
    }
    );
  });
});
