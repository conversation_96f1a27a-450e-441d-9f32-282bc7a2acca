"use client";

import { SessionProvider } from 'next-auth/react';
import React from 'react';

interface SessionWrapperProps {
  children: React.ReactNode;
}

export default function SessionWrapper({
  children,
}: SessionWrapperProps) {
  return (
    <SessionProvider
      refetchInterval={15 * 60} // Refetch every 15 minutes instead of 5
      refetchOnWindowFocus={true} // Refetch when window gains focus
      refetchWhenOffline={false} // Don't refetch when offline
    >
      {children}
    </SessionProvider>
  );
}
